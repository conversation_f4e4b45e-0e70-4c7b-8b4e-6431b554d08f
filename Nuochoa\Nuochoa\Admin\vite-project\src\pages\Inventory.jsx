import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaSync, FaEdit, FaPlus, FaMinus, FaSearch, FaFilter } from 'react-icons/fa';
import './Inventory.css';

const Inventory = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [editingStock, setEditingStock] = useState(null);
  const [stockValue, setStockValue] = useState('');

  // Fetch products
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:4000/api/product/list');
      const data = Array.isArray(response.data) ? response.data : response.data.data;
      setProducts(data);
      setFilteredProducts(data);
    } catch (error) {
      console.error('Error fetching products:', error);
      alert('Lỗi khi tải danh sách sản phẩm');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  // Filter products
  useEffect(() => {
    let filtered = [...products];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(product => {
        const quantity = product.quantity || 0;
        switch (statusFilter) {
          case 'in-stock':
            return quantity > 5;
          case 'low-stock':
            return quantity > 0 && quantity <= 5;
          case 'out-of-stock':
            return quantity === 0;
          default:
            return true;
        }
      });
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, statusFilter]);

  // Update stock
  const updateStock = async (productId, newQuantity, action = 'set') => {
    try {
      const token = localStorage.getItem('adminToken') || 'admin_token_default';
      await axios.put(
        `http://localhost:4000/api/inventory/stock/${productId}`,
        { quantity: newQuantity, action },
        { headers: { token: token } }
      );
      
      fetchProducts(); // Refresh data
      setEditingStock(null);
      setStockValue('');
      alert('Cập nhật tồn kho thành công!');
    } catch (error) {
      console.error('Error updating stock:', error);
      alert('Lỗi khi cập nhật tồn kho');
    }
  };

  // Quick stock actions
  const quickStockAction = async (productId, action, amount = 1) => {
    try {
      const token = localStorage.getItem('adminToken') || 'admin_token_default';
      await axios.put(
        `http://localhost:4000/api/inventory/stock/${productId}`,
        { quantity: amount, action },
        { headers: { token: token } }
      );
      
      fetchProducts();
      alert(`${action === 'add' ? 'Thêm' : 'Trừ'} tồn kho thành công!`);
    } catch (error) {
      console.error('Error updating stock:', error);
      alert('Lỗi khi cập nhật tồn kho');
    }
  };

  const getStockStatus = (quantity) => {
    if (quantity === 0) return 'out-of-stock';
    if (quantity <= 5) return 'low-stock';
    return 'in-stock';
  };

  const getStockStatusText = (quantity) => {
    if (quantity === 0) return 'Hết hàng';
    if (quantity <= 5) return 'Sắp hết';
    return 'Còn hàng';
  };

  // Calculate statistics
  const stats = {
    total: products.length,
    inStock: products.filter(p => (p.quantity || 0) > 5).length,
    lowStock: products.filter(p => (p.quantity || 0) > 0 && (p.quantity || 0) <= 5).length,
    outOfStock: products.filter(p => (p.quantity || 0) === 0).length,
    totalValue: products.reduce((sum, p) => sum + ((p.quantity || 0) * (p.price || 0)), 0)
  };

  if (loading) {
    return <div className="loading">Đang tải dữ liệu...</div>;
  }

  return (
    <div className="inventory-page">
      {/* Header */}
      <div className="inventory-header">
        <div className="header-content">
          <h1>📦 Quản lý tồn kho</h1>
          <p>Theo dõi và cập nhật tồn kho sản phẩm</p>
        </div>
        <button
          className="refresh-btn"
          onClick={fetchProducts}
          disabled={loading}
        >
          <FaSync className={loading ? 'spinning' : ''} /> Làm mới
        </button>
      </div>

      {/* Statistics */}
      <div className="inventory-stats">
        <div className="stat-card total">
          <div className="stat-icon">📦</div>
          <div className="stat-info">
            <div className="stat-number">{stats.total}</div>
            <div className="stat-label">Tổng sản phẩm</div>
          </div>
        </div>
        <div className="stat-card in-stock">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-number">{stats.inStock}</div>
            <div className="stat-label">Còn hàng</div>
          </div>
        </div>
        <div className="stat-card low-stock">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <div className="stat-number">{stats.lowStock}</div>
            <div className="stat-label">Sắp hết</div>
          </div>
        </div>
        <div className="stat-card out-of-stock">
          <div className="stat-icon">❌</div>
          <div className="stat-info">
            <div className="stat-number">{stats.outOfStock}</div>
            <div className="stat-label">Hết hàng</div>
          </div>
        </div>
        <div className="stat-card value">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-number">{stats.totalValue.toLocaleString('vi-VN')}đ</div>
            <div className="stat-label">Giá trị tồn kho</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="inventory-filters">
        <div className="search-box">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Tìm kiếm sản phẩm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="status-filter"
        >
          <option value="all">Tất cả trạng thái</option>
          <option value="in-stock">Còn hàng</option>
          <option value="low-stock">Sắp hết</option>
          <option value="out-of-stock">Hết hàng</option>
        </select>
      </div>

      {/* Products Table */}
      <div className="inventory-table-container">
        <table className="inventory-table">
          <thead>
            <tr>
              <th>Sản phẩm</th>
              <th>Danh mục</th>
              <th>Giá bán</th>
              <th>Tồn kho</th>
              <th>Trạng thái</th>
              <th>Giá trị</th>
              <th>Thao tác</th>
            </tr>
          </thead>
          <tbody>
            {filteredProducts.map((product) => (
              <tr key={product._id} className={`stock-row ${getStockStatus(product.quantity || 0)}`}>
                <td className="product-info">
                  <div className="product-details">
                    <img
                      src={`http://localhost:4000/uploads/${product.image}`}
                      alt={product.name}
                      className="product-image"
                      onError={(e) => {
                        e.target.src = '/placeholder-image.jpg';
                      }}
                    />
                    <div>
                      <div className="product-name">{product.name}</div>
                      <div className="product-brand">{product.brand}</div>
                    </div>
                  </div>
                </td>
                <td>{product.category}</td>
                <td>{(product.price || 0).toLocaleString('vi-VN')}đ</td>
                <td>
                  {editingStock === product._id ? (
                    <div className="stock-edit">
                      <input
                        type="number"
                        value={stockValue}
                        onChange={(e) => setStockValue(e.target.value)}
                        min="0"
                        className="stock-input"
                      />
                      <button
                        onClick={() => updateStock(product._id, parseInt(stockValue))}
                        className="save-btn"
                      >
                        ✓
                      </button>
                      <button
                        onClick={() => {
                          setEditingStock(null);
                          setStockValue('');
                        }}
                        className="cancel-btn"
                      >
                        ✕
                      </button>
                    </div>
                  ) : (
                    <div className="stock-display">
                      <span className="stock-number">{product.quantity || 0}</span>
                      <button
                        onClick={() => {
                          setEditingStock(product._id);
                          setStockValue(product.quantity || 0);
                        }}
                        className="edit-stock-btn"
                      >
                        <FaEdit />
                      </button>
                    </div>
                  )}
                </td>
                <td>
                  <span className={`status-badge ${getStockStatus(product.quantity || 0)}`}>
                    {getStockStatusText(product.quantity || 0)}
                  </span>
                </td>
                <td>{((product.quantity || 0) * (product.price || 0)).toLocaleString('vi-VN')}đ</td>
                <td>
                  <div className="stock-actions">
                    <button
                      onClick={() => quickStockAction(product._id, 'add', 1)}
                      className="quick-btn add"
                      title="Thêm 1"
                    >
                      <FaPlus />
                    </button>
                    <button
                      onClick={() => quickStockAction(product._id, 'subtract', 1)}
                      className="quick-btn subtract"
                      title="Trừ 1"
                      disabled={(product.quantity || 0) === 0}
                    >
                      <FaMinus />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredProducts.length === 0 && (
        <div className="no-products">
          <div className="no-products-icon">📦</div>
          <h3>Không tìm thấy sản phẩm</h3>
          <p>Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
        </div>
      )}
    </div>
  );
};

export default Inventory;
