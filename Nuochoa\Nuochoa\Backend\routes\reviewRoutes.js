// routes/reviewRoutes.js
import express from 'express';
import auth from '../middleware/auth.js';
import Review from '../models/Review.js';

const router = express.Router();

// Test route để debug
router.get('/test', (req, res) => {
  console.log("🧪 Test route called");
  res.json({ success: true, message: "Review routes working!", timestamp: new Date() });
});

// Simple getAllReviews route for testing
router.get('/all', async (req, res) => {
  try {
    console.log("📋 Getting all reviews - simple version");

    // Get all reviews without populate first
    const reviews = await Review.find().sort({ date: -1 }).limit(10);

    console.log("📋 Found reviews:", reviews.length);

    res.json({
      success: true,
      reviews: reviews,
      totalReviews: reviews.length,
      message: "Simple version working"
    });

  } catch (error) {
    console.error("❌ Error:", error);
    res.json({
      success: false,
      message: "Error: " + error.message
    });
  }
});

export default router;
