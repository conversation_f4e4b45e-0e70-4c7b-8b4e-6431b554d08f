.inventory-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.header-content p {
  color: #6c757d;
  margin: 0;
}

.refresh-btn {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Statistics */
.inventory-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.stat-card.total .stat-number { color: #495057; }
.stat-card.in-stock .stat-number { color: #28a745; }
.stat-card.low-stock .stat-number { color: #fd7e14; }
.stat-card.out-of-stock .stat-number { color: #dc3545; }
.stat-card.value .stat-number { color: #007bff; font-size: 18px; }

/* Filters */
.inventory-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.status-filter {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

/* Table */
.inventory-table-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.inventory-table {
  width: 100%;
  border-collapse: collapse;
}

.inventory-table th {
  background: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
}

.inventory-table td {
  padding: 15px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.stock-row.out-of-stock {
  background-color: #fff5f5;
}

.stock-row.low-stock {
  background-color: #fffbf0;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
}

.product-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.product-brand {
  font-size: 12px;
  color: #6c757d;
}

.stock-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stock-number {
  font-weight: 600;
  font-size: 16px;
}

.edit-stock-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-stock-btn:hover {
  background-color: #f8f9fa;
}

.stock-edit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.save-btn, .cancel-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
}

.save-btn {
  background: #28a745;
  color: white;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.in-stock {
  background: #d4edda;
  color: #155724;
}

.status-badge.low-stock {
  background: #fff3cd;
  color: #856404;
}

.status-badge.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

.stock-actions {
  display: flex;
  gap: 8px;
}

.quick-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s;
}

.quick-btn.add {
  background: #28a745;
  color: white;
}

.quick-btn.add:hover {
  background: #218838;
}

.quick-btn.subtract {
  background: #dc3545;
  color: white;
}

.quick-btn.subtract:hover:not(:disabled) {
  background: #c82333;
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.no-products {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.no-products-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #6c757d;
}
