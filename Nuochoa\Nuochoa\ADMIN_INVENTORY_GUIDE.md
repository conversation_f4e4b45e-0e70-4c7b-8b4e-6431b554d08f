# Hướng dẫn sử dụng trang Quản lý Tồn kho Admin

## 🎯 Tổng quan

Trang quản lý tồn kho giúp admin theo dõi và cập nhật số lượng sản phẩm trong kho một cách real-time. Trang này đã được tích hợp vào admin panel để giải quyết vấn đề tồn kho không cập nhật khi có đơn hàng.

## 🔧 Các tính năng đã thêm

### 1. Trang Products (Cải tiến)
- ✅ **Hiển thị tồn kho**: Mỗi sản phẩm hiện hiển thị số lượng tồn kho
- ✅ **Cảnh báo trạng thái**: 
  - 🟢 Còn hàng (>5 sản phẩm)
  - 🟡 S<PERSON><PERSON> hết (1-5 sản phẩm) 
  - 🔴 Hết hàng (0 sản phẩm)
- ✅ **Thống kê tổng quan**: Hiển thị số liệu tồn kho ở đầu trang
- ✅ **Nút làm mới**: C<PERSON><PERSON> nhật dữ liệu real-time

### 2. Trang Inventory (Mới)
- ✅ **Quản lý tồn kho chuyên sâu**: Trang riêng để quản lý tồn kho
- ✅ **Bảng chi tiết**: Hiển thị đầy đủ thông tin sản phẩm và tồn kho
- ✅ **Chỉnh sửa nhanh**: Click để chỉnh sửa số lượng tồn kho
- ✅ **Thao tác nhanh**: Nút +/- để tăng/giảm tồn kho
- ✅ **Bộ lọc**: Lọc theo trạng thái tồn kho
- ✅ **Tìm kiếm**: Tìm sản phẩm theo tên hoặc thương hiệu

## 📍 Cách truy cập

### Từ Sidebar:
1. Đăng nhập admin panel
2. Click "Quản lý tồn kho" trong sidebar
3. Hoặc truy cập: `/inventory`

### Từ Dashboard:
1. Click vào thẻ "Kiểm tra kho" trong phần thao tác nhanh

## 🎮 Cách sử dụng

### Xem thống kê tồn kho:
- **Tổng sản phẩm**: Số lượng sản phẩm trong hệ thống
- **Còn hàng**: Sản phẩm có >5 trong kho
- **Sắp hết**: Sản phẩm có 1-5 trong kho
- **Hết hàng**: Sản phẩm có 0 trong kho
- **Giá trị tồn kho**: Tổng giá trị tiền của tồn kho

### Cập nhật tồn kho:

#### Phương pháp 1: Chỉnh sửa trực tiếp
1. Click vào icon ✏️ bên cạnh số lượng tồn kho
2. Nhập số lượng mới
3. Click ✓ để lưu hoặc ✕ để hủy

#### Phương pháp 2: Thao tác nhanh
1. Click nút ➕ để thêm 1 sản phẩm
2. Click nút ➖ để trừ 1 sản phẩm

### Lọc và tìm kiếm:
- **Tìm kiếm**: Nhập tên sản phẩm hoặc thương hiệu
- **Lọc trạng thái**: Chọn "Còn hàng", "Sắp hết", "Hết hàng"

## 🔄 Tích hợp với hệ thống đặt hàng

### Tự động cập nhật tồn kho:
- ✅ **COD**: Tồn kho giảm ngay khi đặt hàng
- ✅ **Stripe/PayOS**: Tồn kho giảm khi thanh toán thành công
- ✅ **Validation**: Kiểm tra tồn kho trước khi cho phép đặt hàng

### Real-time updates:
- Tồn kho được cập nhật ngay lập tức khi có đơn hàng
- Admin có thể thấy thay đổi bằng cách click "Làm mới"

## 🎨 Giao diện

### Màu sắc trạng thái:
- 🟢 **Xanh lá**: Còn hàng (an toàn)
- 🟡 **Cam**: Sắp hết (cảnh báo)
- 🔴 **Đỏ**: Hết hàng (nguy hiểm)

### Responsive design:
- Tối ưu cho desktop và tablet
- Bảng có thể cuộn ngang trên mobile

## 🔧 Troubleshooting

### Nếu tồn kho không cập nhật:
1. Click nút "Làm mới" để tải lại dữ liệu
2. Kiểm tra kết nối internet
3. Đăng xuất và đăng nhập lại

### Nếu không thể chỉnh sửa tồn kho:
1. Đảm bảo đã đăng nhập với quyền admin
2. Kiểm tra token admin trong localStorage
3. Thử refresh trang

### Nếu thống kê không chính xác:
1. Click "Làm mới" để cập nhật dữ liệu
2. Kiểm tra xem có đơn hàng mới không
3. Xem log trong console để debug

## 📊 API Endpoints sử dụng

### Lấy danh sách sản phẩm:
```
GET /api/product/list
```

### Cập nhật tồn kho:
```
PUT /api/inventory/stock/:productId
Body: { quantity: number, action: 'set'|'add'|'subtract' }
Headers: { token: 'admin_token' }
```

### Lấy thống kê tồn kho:
```
GET /api/inventory/stats
Headers: { token: 'admin_token' }
```

## 🚀 Tính năng tương lai

### Đang phát triển:
- [ ] Lịch sử xuất nhập kho
- [ ] Cảnh báo tồn kho thấp qua email
- [ ] Import/Export dữ liệu tồn kho
- [ ] Báo cáo tồn kho theo thời gian
- [ ] Quản lý nhà cung cấp

### Có thể thêm:
- [ ] Barcode scanning
- [ ] Tích hợp với hệ thống POS
- [ ] Dự báo nhu cầu
- [ ] Quản lý kho theo địa điểm

## 📞 Hỗ trợ

Nếu gặp vấn đề với trang quản lý tồn kho:
1. Kiểm tra console browser để xem lỗi
2. Đảm bảo server backend đang chạy
3. Kiểm tra quyền admin
4. Liên hệ developer để hỗ trợ

---

**Lưu ý**: Trang này yêu cầu quyền admin để truy cập và sử dụng. Đảm bảo đã đăng nhập với tài khoản admin hợp lệ.
